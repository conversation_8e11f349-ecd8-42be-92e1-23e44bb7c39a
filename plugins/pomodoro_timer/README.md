# 番茄钟插件

专业的番茄工作法计时器，帮助提高工作效率和专注力。

## 功能特性

- 🍅 标准番茄工作法计时
- ⏰ 自定义工作和休息时长
- 🔄 自动循环工作和休息
- 📊 工作周期统计
- 🔔 声音和桌面通知
- ⚙️ 丰富的个性化设置
- 🎯 专注力提升工具

## 安装方法

### 通过插件商城安装
1. 打开TimeNest
2. 右键系统托盘图标 → 插件管理
3. 在插件商城中搜索"番茄钟插件"
4. 点击安装按钮

## 使用说明

### 基本操作
- **开始**：开始当前计时周期
- **暂停**：暂停当前计时
- **重置**：重置计时器到初始状态

### 工作流程
1. 点击"开始"开始25分钟工作时间
2. 工作时间结束后自动进入5分钟短休息
3. 完成4个工作周期后进入15分钟长休息
4. 循环重复上述过程

### 设置配置

#### 时间设置
- **工作时长**：单次工作时间（5-60分钟，默认25分钟）
- **短休息**：短休息时间（1-15分钟，默认5分钟）
- **长休息**：长休息时间（5-30分钟，默认15分钟）
- **长休息周期**：多少个工作周期后进行长休息（2-8个，默认4个）

#### 自动化设置
- **自动开始休息**：工作结束后自动开始休息
- **自动开始工作**：休息结束后自动开始工作

#### 提醒设置
- **声音提醒**：时间结束时播放提示音
- **桌面通知**：显示桌面通知消息
- **浮窗显示**：在TimeNest浮窗中显示计时器

## 番茄工作法介绍

番茄工作法是一种时间管理方法，由弗朗西斯科·西里洛于1980年代末发明。该方法使用一个定时器来分割出一个一般为25分钟的工作时间和5分钟的休息时间。

### 基本原理
1. **专注工作**：25分钟内专注于单一任务
2. **短暂休息**：5分钟休息，放松大脑
3. **循环重复**：重复工作-休息循环
4. **长时间休息**：每4个周期后进行15-30分钟长休息

### 使用技巧
- 在工作时间内避免任何干扰
- 休息时间完全放松，不要思考工作
- 记录完成的番茄钟数量
- 根据个人情况调整时间长度

## 开发说明

### 插件结构
```
pomodoro_timer/
├── manifest.json    # 插件元数据
├── plugin.py       # 主插件代码
├── README.md       # 说明文档
└── screenshots/    # 截图目录
```

### 核心类
- `PomodoroTimerPlugin`：主插件类
- `PomodoroWidget`：计时器显示组件

### 主要功能
- 计时器管理
- 状态切换（工作/休息）
- 周期统计
- 通知发送
- 设置管理

## API接口

### 控制计时器
```python
plugin.start_timer()    # 开始计时
plugin.pause_timer()    # 暂停计时
plugin.reset_timer()    # 重置计时器
```

### 获取状态
```python
is_running = plugin.is_running      # 是否正在运行
is_work = plugin.is_work_time       # 是否工作时间
time_left = plugin.time_left        # 剩余时间（秒）
cycles = plugin.cycle_count         # 完成周期数
```

## 许可证

MIT License

## 作者

Productivity Team

## 版本历史

- v2.1.0：新增自定义时长设置，优化通知提醒
- v2.0.0：重构界面，新增统计功能
- v1.0.0：初始版本发布

## 支持

如有问题或建议，请访问：
- GitHub: https://github.com/ziyi127/TimeNest-Store
- Issues: https://github.com/ziyi127/TimeNest-Store/issues

## 相关资源

- [番茄工作法官方网站](https://francescocirillo.com/pages/pomodoro-technique)
- [时间管理技巧](https://en.wikipedia.org/wiki/Time_management)
- [专注力训练方法](https://www.mindtools.com/pages/article/improving-concentration.htm)
